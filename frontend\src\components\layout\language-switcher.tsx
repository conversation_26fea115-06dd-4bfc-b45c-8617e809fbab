import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Globe2 } from 'lucide-react';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';

const LANG_COOKIE = 'lang';

function setLangCookie(lang: string) {
  document.cookie = `${LANG_COOKIE}=${lang}; path=/; max-age=31536000`;
}

function getLangCookie(): string | undefined {
  const match = document.cookie.match(new RegExp('(^| )' + LANG_COOKIE + '=([^;]+)'));
  return match ? match[2] : undefined;
}

const LANGUAGE_ALIASES: Record<string, string> = {
  id: 'ID',
  en: 'EN',
  'zh-CN': '简体',
};

export function LanguageSwitcher() {
  const { i18n, t } = useTranslation();
  const [open, setOpen] = React.useState(false);
  const [selected, setSelected] = React.useState(i18n.language);

  useEffect(() => {
    const cookieLang = getLangCookie();
    if (cookieLang && cookieLang !== i18n.language) {
      i18n.changeLanguage(cookieLang);
      setSelected(cookieLang);
    }
  }, []);

  const handleSelect = (lang: string) => {
    i18n.changeLanguage(lang);
    setSelected(lang);
    setLangCookie(lang);
    setOpen(false);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button size={'sm'} aria-label={t('Select language')} variant="outline">
          <Globe2 className="w-5 h-5" />
          <span className="uppercase font-semibold">{LANGUAGE_ALIASES[selected] || selected.toUpperCase()}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem onClick={() => handleSelect('id')} className={selected === 'id' ? 'font-semibold' : ''}>
          Bahasa Indonesia
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleSelect('en')} className={selected === 'en' ? 'font-semibold' : ''}>
          English
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleSelect('zh-CN')} className={selected === 'zh-CN' ? 'font-semibold' : ''}>
          简体中文
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 