import type { BusinessPartnerDto, LocalVesselWithItemsDto, MasterTenantDto } from '@/clientEkb/types.gen';
import type { QueryClient } from '@tanstack/react-query';
import Handsontable from 'handsontable';
import type { LocalVesselHeaderForm } from './local-vessel-header-schema';
import type { LocalVesselItemForm } from './local-vessel-item-schema';

// Type for row data that includes id and status
type TableRowData = LocalVesselItemForm & {
  id?: string;
  status?: string;
  concurrencyStamp?: string;
  headerId?: string;
  docNum?: number | undefined;
  agentId?: string;
};

/**
 * Generates and opens an application document for preview
 */
async function generateAndOpenDocument(
  rowData: TableRowData,
  onPreview: (documentSrc: string) => void,
  setLoading: (loading: boolean) => void,
  header?: LocalVesselHeaderForm,
  vesselData?: LocalVesselWithItemsDto // Full vessel data from API with master objects
) {
  try {
    setLoading(true);

    // Create the document data payload from row data and header
    const documentData = {
      tenantName: rowData.tenant || '',
      itemName: rowData.itemName || '',
      qty: rowData.itemQty || 0,
      uoM: rowData.unitQty || '',
      notes: rowData.remarks || '',
      status: rowData.status || '',
      letterNo: rowData.letterNo || '',
      letterDate: rowData.letterDate ? rowData.letterDate : null,
      docNum: Number(header?.docNum) || 0,
      vesselName: vesselData?.vessel?.name || header?.vesselId || '',
      voyage: header?.voyage || '',
      jetty: vesselData?.masterJetty?.name || header?.jettyId || '',
      arrivalDate: header?.vesselArrival || null,
      departureDate: header?.vesselDeparture || null,
      portOrigin: vesselData?.masterPortOrigin?.name || header?.portOriginId || '',
      destinationPort: vesselData?.masterDestinationPort?.name || header?.destinationPortId || '',
      generatedDate: new Date().toISOString(),
      generatedBy: '', // This could be set from current user
    };

    const input = {
      generatePdf: true,
      documentData: documentData,
    };

    // Use the generated SDK function
    const { postApiIdjasApplicationDocumentGenerateApplicationDocumentWithPayloadAsAttachment } = await import('@/client/sdk.gen');
    const response = await postApiIdjasApplicationDocumentGenerateApplicationDocumentWithPayloadAsAttachment({
      body: input
    });

    if (response.data) {
      // The response.data is the custom object from the controller
      const result = response.data;
      if (result.streamUrl) {
        // Open the document in the preview dialog
        onPreview(result.streamUrl);
      } else {
        console.error('Failed to generate document:', result);
        alert('Failed to generate document. Please try again.');
      }
    } else {
      console.error('No response data received');
      alert('Failed to generate document. Please try again.');
    }
  } catch (error) {
    console.error('Error generating document:', error);
    alert('Error generating document. Please try again.');
  } finally {
    setLoading(false);
  }
}

/**
 * Submits a document for approval
 */
async function submitForApproval(
  jettyRequestItemId: string,
  documentType: string,
  setLoading: (loading: boolean) => void,
  queryClient: QueryClient,
  jettyRequestId: string,
  rowData: TableRowData // Added rowData parameter
) {
  try {
    setLoading(true);

    const { postApiIdjasApprovalSubmit } = await import('@/client/sdk.gen');
    const { toast } = await import('@/lib/useToast');

    const input = {
      documentId: jettyRequestItemId,
      documentType: documentType,
      notes: 'Submitted for approval'
    };

    const response = await postApiIdjasApprovalSubmit({
      body: input
    });

    if (response.data) {
      toast({
        title: 'Success',
        description: 'Successfully submitted for approval',
        variant: 'default',
      });
      // Update status to Pending after approval submission
      try {
        const { putApiEkbBoundedZoneById } = await import('@/clientEkb/sdk.gen');
        await putApiEkbBoundedZoneById({
          path: { id: jettyRequestItemId },
          body: {
            ...rowData,
            tenantId: rowData.tenantId ?? '',
            concurrencyStamp: rowData.concurrencyStamp,
            headerId: rowData.headerId ?? rowData.headerId ?? '',
            docNum: rowData.docNum ?? rowData.docNum ?? 0,
            agentId: rowData.agentId ?? rowData.agentId ?? '',
            status: 'Pending',
          }
        });
        await queryClient.refetchQueries({ queryKey: ['local-vessel'] });
        toast({
          title: 'Status Updated',
          description: 'Status updated to Pending',
          variant: 'default',
        });
      } catch (_err: unknown) {
        console.log("error", _err)
        toast({
          title: 'Status Update Failed',
          description: 'Failed to update status to Pending',
          variant: 'destructive',
        });
      }
      await queryClient.refetchQueries({ queryKey: ['local-vessel', jettyRequestId] });
    } else {
      toast({
        title: 'Error',
        description: 'Failed to submit for approval',
        variant: 'destructive',
      });
    }
  } catch (error: unknown) {
    console.error('Error submitting for approval:', error);

    const { toast } = await import('@/lib/useToast');
    let message = 'Error submitting for approval. Please try again.';
    if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
      message = (error as { message?: string }).message ?? message;
    }

    toast({
      title: 'Error submitting for approval',
      description: message,
      variant: 'destructive',
    });
  } finally {
    setLoading(false);
  }
}

// Place PreviewButtonProps and PreviewButton at the top level

export const renderPreviewButton = (
  tableData: TableRowData[],
  onPreview: (documentSrc: string) => void,
  loadingStates: Map<number, boolean>,
  setLoadingState: (row: number, loading: boolean) => void,
  header?: LocalVesselHeaderForm,
  vesselData?: LocalVesselWithItemsDto // Full vessel data from API with master objects
) => (
  _instance: Handsontable.Core | undefined,
  td: HTMLTableCellElement,
  _row: number,
  _col: number,
  _prop: string | number,
  _value: unknown,
  _cellProperties: Handsontable.CellProperties
) => {
    void _col;
    void _prop;
    void _value;
    void _cellProperties;
    const rowData = tableData[_row];

    const hasId = rowData?.id;
    const isLoading = loadingStates.get(_row) || false;

    const buttonClass = hasId && !isLoading
      ? 'px-2 py-0.5 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50'
      : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';

    const buttonTitle = hasId
      ? (isLoading ? 'Generating document...' : 'Preview document')
      : 'Save the application first to enable preview';

    const disabledAttr = hasId && !isLoading ? '' : 'disabled';
    const buttonText = isLoading ? 'Loading...' : 'Preview';

    const previewButton = `<button class="${buttonClass}" data-row="${_row}" data-action="preview" title="${buttonTitle}" ${disabledAttr}>${buttonText}</button>`;
    td.innerHTML = previewButton;

    const previewBtn = td.querySelector('[data-action="preview"]');
    if (previewBtn && hasId && !isLoading) {
      previewBtn.addEventListener('click', async () => {
        const rowData = tableData[_row];
        if (rowData?.id) {
          await generateAndOpenDocument(
            rowData,
            onPreview,
            (loading) => setLoadingState(_row, loading),
            header,
            vesselData
          );
        }
      });
    }
  };

export const renderSubmitButton = (
  tableData: TableRowData[],
  documentType: string,
  loadingStates: Map<number, boolean>,
  setLoadingState: (row: number, loading: boolean) => void,
  queryClient: QueryClient,
  jettyRequestId: string
) => (
  _instance: Handsontable.Core | undefined,
  td: HTMLTableCellElement,
  _row: number,
  _col: number,
  _prop: string | number,
  _value: unknown,
  _cellProperties: Handsontable.CellProperties
) => {
    void _col;
    void _prop;
    void _value;
    void _cellProperties;
    const rowData = tableData[_row];

    const hasId = rowData?.id;
    const isLoading = loadingStates.get(_row) || false;

    // Always render the button, just like preview
    const buttonClass = hasId && !isLoading
      ? 'px-2 py-0.5 bg-green-500 text-white rounded-md text-xs hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50'
      : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';

    const buttonTitle = hasId
      ? (isLoading ? 'Submitting for approval...' : 'Submit for approval')
      : 'Save the application first to enable submit';

    const disabledAttr = hasId && !isLoading ? '' : 'disabled';
    const buttonText = isLoading ? 'Submitting...' : 'Submit';

    const submitButton = `<button class="${buttonClass}" data-row="${_row}" data-action="submit" title="${buttonTitle}" ${disabledAttr}>${buttonText}</button>`;
    td.innerHTML = submitButton;

    const submitBtn = td.querySelector('[data-action="submit"]');
    if (submitBtn && hasId && !isLoading) {
      submitBtn.addEventListener('click', async () => {
        const rowData = tableData[_row];
        if (rowData?.id) {
          await submitForApproval(
            rowData.id,
            documentType,
            (loading) => setLoadingState(_row, loading),
            queryClient,
            jettyRequestId,
            rowData // Pass rowData to submitForApproval
          );
        }
      });
    }
  };

// Renderer factory for attachment button
export const renderAttachmentButton = (queryClient: QueryClient) => {
  if (!queryClient) throw new Error('queryClient is required for renderAttachmentButton');
  return (
    _instance: Handsontable.Core | undefined,
    td: HTMLTableCellElement,
    _row: number,
    _col: number,
    _prop: string | number,
    _value: unknown,
    _cellProperties: Handsontable.CellProperties
  ) => {
    void _col;
    void _prop;
    void _value;
    void _cellProperties;

    // Defensive: clear previous content
    td.innerHTML = '';

    if (!_instance) return;

    const attachments = _instance.getDataAtRowProp(_row, 'attachments') || [];
    const itemName = _instance.getDataAtRowProp(_row, 'itemName') || '';
    // const rowDataRaw = _instance.getSourceDataAtRow?.(_row);
    // const rowData = (rowDataRaw && !Array.isArray(rowDataRaw)) ? rowDataRaw : {};

    const buttonClass = attachments.length > 0
      ? 'px-2 py-0.5 bg-emerald-500 text-white rounded-md text-xs hover:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-opacity-50'
      : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';

    const buttonTitle = attachments.length > 0
      ? `View Attachments (${attachments.length})`
      : 'No attachments';

    const disabledAttr = attachments.length > 0 ? '' : 'disabled';

    const attachmentButton = `<button class="${buttonClass}" data-row="${_row}" data-action="attachment" title="${buttonTitle}" ${disabledAttr}>Attachment (${attachments.length})</button>`;
    td.innerHTML = attachmentButton;

    const btn = td.querySelector('[data-action="attachment"]');
    if (btn && attachments.length > 0) {
      btn.addEventListener('click', () => {
        // TODO: Replace this with your actual dialog logic
        window.alert(`Show attachments for ${itemName} (implement dialog logic here)`);
      });
    }
  };
};

export const getLocalVesselColumns = (
  tenants: MasterTenantDto[],
  businessPartners: BusinessPartnerDto[],
  queryClient: QueryClient,
  tableData: TableRowData[] = [],
  onPreview?: (documentSrc: string) => void,
  loadingStates?: Map<number, boolean>,
  setLoadingState?: (row: number, loading: boolean) => void,
  jettyRequestId?: string,
  header?: LocalVesselHeaderForm,
  vesselData?: LocalVesselWithItemsDto // Full vessel data from API with master objects
) => {
  // Extract tenant names for autocomplete source
  const tenantNames = tenants.map(t => t.name ?? '').filter(name => name !== '');

  // Extract business partner names for autocomplete source
  const businessPartnerNames = businessPartners.map(bp => bp.name ?? '').filter(name => name !== '');

  return [
    { data: 'id', title: 'Id', type: 'text', width: 200 },
    { data: 'docEntry', title: 'DocEntry', type: 'text', width: 200 },
    { data: 'concurrencyStamp', title: 'concurrencyStamp', type: 'text', width: 200 },
    {
      data: 'tenant',
      title: 'Tenant',
      type: 'autocomplete',
      width: 140,
      source: tenantNames,
      strict: false,
      allowInvalid: false,
      trimDropdown: false,
      visibleRows: 6,
    },
    {
      data: 'businessPartner',
      title: 'Business Partner',
      type: 'autocomplete',
      width: 250,
      source: businessPartnerNames,
      strict: false,
      allowInvalid: false,
      trimDropdown: false,
      visibleRows: 6,
    },
    { data: 'itemName', title: 'Item Name', type: 'text', width: 200 },
    { data: 'itemQty', title: 'Quantity', type: 'numeric', width: 80 },
    { data: 'unitQty', title: 'UOM', type: 'text', width: 100 },
    { data: 'remarks', title: 'Remark', type: 'text', width: 120 },
    { data: 'letterNo', title: 'Letter No', type: 'text', width: 120 },
    { data: 'letterDate', title: 'Letter Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    { data: 'noBl', title: 'No BL', type: 'text', width: 120 },
    { data: 'dateBl', title: 'Date BL', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    { data: 'ajuNo', title: 'AJU No', type: 'text', width: 120 },
    { data: 'regNo', title: 'Reg No', type: 'text', width: 120 },
    { data: 'regDate', title: 'Reg Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    { data: 'sppbNo', title: 'SPPB No', type: 'text', width: 120 },
    { data: 'sppbDate', title: 'SPPB Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    { data: 'sppdNo', title: 'SPPD No', type: 'text', width: 120 },
    { data: 'sppdDate', title: 'SPPD Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },
    { data: 'grossWeight', title: 'Gross Weight', type: 'numeric', width: 100 },
    { data: 'unitWeight', title: 'Unit Weight', type: 'text', width: 100 },
    { data: 'status', title: 'Status', type: 'text', width: 100, readOnly: true },
    { data: 'attachments', title: 'Attachment', width: 130, renderer: renderAttachmentButton(queryClient), readOnly: true, filterable: false },
    {
      data: 'preview',
      title: 'Preview',
      width: 100,
      renderer: onPreview && loadingStates && setLoadingState
        ? renderPreviewButton(tableData, onPreview, loadingStates, setLoadingState, header, vesselData)
        : undefined,
      readOnly: true,
      filterable: false
    },
    {
      data: 'submit',
      title: 'Submit',
      width: 100,
      renderer: renderSubmitButton(tableData, 'Local', loadingStates!, setLoadingState!, queryClient, jettyRequestId || ''),
      readOnly: true,
      filterable: false
    },
  ];
};

// Legacy export for backward compatibility
export const localVesselColumns = getLocalVesselColumns([], [], {} as QueryClient); 