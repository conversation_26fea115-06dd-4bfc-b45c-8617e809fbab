{"menu": {"dashboard": "Dashboard", "jettyApplication": "Jetty Application", "newApplication": "New Application", "applicationList": "Application List", "approvalManagement": "Approval Management", "incomingApproval": "Incoming Approval", "history": "History", "customAreaVessel": "Customs Area Vessel", "exportVessel": "Export <PERSON>", "importVessel": "<PERSON><PERSON><PERSON>", "localVessel": "Local Vessel", "nonCustomAreaVessel": "Non-Customs Area Vessel", "jettyOperations": "Jetty Operations", "masterData": "Master Data", "manageJetty": "Manage Jetty", "manageCargo": "Manage Cargo", "masterReport": "Master Report", "reports": "Reports", "documentTemplate": "Document Template", "approvalTemplate": "Approval Template"}, "table": {"docNum": "DocNum", "vesselName": "Vessel Name", "voyage": "Voyage", "arrivalDate": "Arrival Date", "departureDate": "Departure Date", "jetty": "<PERSON>y", "portOrigin": "Port Origin", "destinationPort": "Destination Port", "actions": "Actions", "docType": "DocType", "bargeName": "Barge Name", "docStatus": "Doc Status", "vesselType": "Vessel Type", "arrival": "Arrival", "requestDate": "Request Date", "status": "Status", "requester": "Requester", "approveRequest": "Approve Request", "rejectRequest": "Reject Request", "viewDetails": "View Details", "approver": "Approver", "statusPending": "Pending", "statusApproved": "Approved", "statusRejected": "Rejected", "statusCancelled": "Cancelled", "statusUnknown": "Unknown"}, "datagrid": {"customArea": {"localVessel": "Custom Area Local Vessel", "importVessel": "Custom Area Import Vessel", "exportVessel": "Custom Area Export Vessel"}, "nonCustomArea": {"localVessel": "Non Custom Area Local Vessel", "importVessel": "Non Custom Area Import Vessel", "exportVessel": "Non Custom Area Export Vessel"}, "localVessel": "Local Vessel", "importVessel": "<PERSON><PERSON><PERSON>", "exportVessel": "Export <PERSON>", "pendingApprovals": "Pending Approvals", "approvalHistory": "Approval History"}, "pages": {"vessel": {"create": {"export": "Create Export <PERSON>el", "import": "Create <PERSON><PERSON><PERSON>", "local": "Create Local Vessel"}, "edit": {"export": "Edit Export V<PERSON>el", "import": "Edit Import <PERSON>", "local": "Edit Local Vessel"}}}}