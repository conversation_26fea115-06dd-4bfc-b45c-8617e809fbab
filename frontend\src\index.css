@import "tailwindcss";
@import "tw-animate-css";

.ht-theme-main, .ht-theme-main-dark, .ht-theme-main-dark-auto{
    --ht-font-size: 12px !important;
    --ht-line-height: 20px !important;
    --ht-font-weight: 400 !important;
    --ht-gap-size: 2px !important;
    --ht-icon-size: 15px !important;
    --ht-table-transition: 0.15s !important;
    --ht-accent-color: #388E3C !important;
    --ht-cell-horizontal-padding: 8px !important;
    --ht-cell-vertical-padding: 3px !important;
    --ht-cell-editor-border-width: 2px !important;
    --ht-cell-editor-border-color: #388E3C !important;
    --ht-cell-selection-border-color: #388E3C !important;
    --ht-cell-selection-background-color: #66BB6A !important;
    --ht-cell-autofill-size: 6px !important;
    --ht-cell-autofill-border-width: 1px !important;
    --ht-cell-autofill-border-radius: 4px !important;
    --ht-cell-autofill-background-color: #388E3C !important;
    --ht-cell-mobile-handle-size: 12px !important;
    --ht-cell-mobile-handle-border-width: 1px !important;
    --ht-cell-mobile-handle-border-radius: 6px !important;
    --ht-cell-mobile-handle-border-color: #388E3C !important;
    --ht-move-indicator-color: #388E3C !important;
    --ht-checkbox-size: 16px !important;
    --ht-checkbox-border-radius: 4px !important;
    --ht-checkbox-focus-ring-color: #388E3C !important;
    --ht-checkbox-checked-background-color: #388E3C !important;
    --ht-checkbox-checked-focus-background-color: #388E3C !important;
    --ht-header-font-weight: 400 !important;
    --ht-header-foreground-color: #222 !important;
    --ht-header-active-border-color: #43A047 !important;
    --ht-header-active-background-color: #388E3C !important;
    --ht-header-highlighted-shadow-size: 0 !important;
    --ht-header-row-active-background-color: #388E3C !important;
    --ht-icon-active-button-border-color: #43A047 !important;
    --ht-icon-active-button-background-color: #388E3C !important;
    --ht-icon-active-button-hover-border-color: #43A047 !important;
    --ht-icon-active-button-hover-background-color: #43A047 !important;
    --ht-button-border-radius: 4px !important;
    --ht-button-horizontal-padding: 12px !important;
    --ht-button-vertical-padding: 6px !important;
    --ht-primary-button-background-color: #388E3C !important;
    --ht-primary-button-focus-background-color: #388E3C !important;
    --ht-comments-textarea-horizontal-padding: 8px !important;
    --ht-comments-textarea-vertical-padding: 4px !important;
    --ht-comments-indicator-size: 6px !important;
    --ht-comments-indicator-color: #388E3C !important;
    --ht-comments-textarea-focus-border-width: 1px !important;
    --ht-comments-textarea-focus-border-color: #388E3C !important;
    --ht-license-horizontal-padding: 16px !important;
    --ht-license-vertical-padding: 8px !important;
    --ht-link-color: #388E3C !important;
    --ht-input-border-width: 1px !important;
    --ht-input-border-radius: 4px !important;
    --ht-input-horizontal-padding: 12px !important;
    --ht-input-vertical-padding: 6px !important;
    --ht-input-focus-border-color: #388E3C !important;
}

.ht-theme-horizon, .ht-theme-horizon-dark, .ht-theme-horizon-dark-auto {
  --ht-font-size: 12px !important;
  --ht-line-height: 20px !important;
  --ht-font-weight: 400;
  --ht-gap-size: 2px !important;
  --ht-icon-size: 14px !important;
  --ht-cell-horizontal-padding: 8px !important;
  --ht-cell-vertical-padding: 6px !important;
}

@custom-variant dark (&:is(.dark *));

.ht_clone_top {
  z-index: 9 !important;
}

.ht_clone_inline_start {
  z-index: 9 !important;
}
.ht_clone_bottom {
  z-index: 9 !important;
}
.ht_clone_top_inline_start_corner {
  z-index: 9 !important;
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.596 0.145 163.225);
  --primary-foreground: oklch(0.979 0.021 166.113);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.637 0.237 25.331);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.871 0.006 286.286);
  --ring: oklch(0.871 0.006 286.286);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.37 0.013 285.805);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.871 0.006 286.286);
}
.dark {
  --background: oklch(0.21 0.006 285.885);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.596 0.145 163.225);
  --primary-foreground: oklch(0.979 0.021 166.113);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.244 0.006 285.97); /* Custom */
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.244 0.006 285.97); /* Custom */
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.637 0.237 25.331);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.29 0.009 285.83); /* Custom */
  --input: oklch(0.29 0.009 285.83); /* Custom */
  --ring: oklch(0.442 0.017 285.786);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.244 0.006 285.97); /* Custom */
  --sidebar-foreground: oklch(0.967 0.001 286.375);
  --sidebar-primary: oklch(0.596 0.145 163.225);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.967 0.001 286.375);
  --sidebar-border: oklch(0.274 0.006 286.033);
  --sidebar-ring: oklch(0.442 0.017 285.786);
}

@theme inline {
  --font-sans:
    var(--font-sans), ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

   /** Animations **/
   --animate-marquee: marquee var(--duration) infinite linear;
   --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;

   @keyframes marquee {
     from {
       transform: translateX(0);
     }
     to {
       transform: translateX(calc(-100% - var(--gap)));
     }
   }
   @keyframes marquee-vertical {
     from {
       transform: translateY(0);
     }
     to {
       transform: translateY(calc(-100% - var(--gap)));
     }
   }
   --animate-accordion-down: accordion-down 0.2s ease-out;
   --animate-accordion-up: accordion-up 0.2s ease-out;
  @keyframes accordion-down {
  from {
    height: 0;
        }
  to {
    height: var(--radix-accordion-content-height);
        }
    }
  @keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
        }
  to {
    height: 0;
        }
    }}

/** Global Styles **/
@layer base {
  * {
    @apply border-border;
  }

  *:focus-visible {
    @apply outline-ring rounded-xs shadow-none outline-2 outline-offset-3 transition-none!;
  }
}

/** Custom Scrollbar **/
@layer base {
  ::-webkit-scrollbar {
    width: 5px;
  }
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  ::-webkit-scrollbar-thumb {
    background: var(--input);
    border-radius: 5px;
  }
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--input) transparent;
  }
}

/** Custom Container **/
@utility container {
  margin-inline: auto;
  padding-inline: 1.5rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1440px) {
    padding-inline: 2rem;
    max-width: 1440px;
  }
}

/** Smooth scroll **/
html {
  scroll-behavior: smooth;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    }
  body {
    @apply bg-background text-foreground;
    }
}

.handsontable.listbox {
  width: 400px !important;
  height: 300px !important;
}