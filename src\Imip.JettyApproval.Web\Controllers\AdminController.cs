using Imip.JettyApproval.Web.Services.Interfaces;
using InertiaCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.JettyApproval.Web.Controllers;

[Authorize]
public class AdminController : AbpController
{
    private readonly ITokenService _tokenService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AdminController> _logger;
    private readonly string _EkbApiBaseUrl;

    public AdminController(
        ITokenService tokenService,
        IConfiguration configuration,
        ILogger<AdminController> logger)
    {
        _tokenService = tokenService;
        _configuration = configuration;
        _logger = logger;
        _EkbApiBaseUrl = _configuration["ExternalApps:EkbApi:BaseUrl"];

    }

    [HttpGet("/")]
    public async Task<IActionResult> Index()
    {
        await Task.CompletedTask;
        return Inertia.Render("home", new
        {
            message = "Welcome to Imip.Ekb Dashboard",
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/admin")]
    public async Task<IActionResult> AdminPage()
    {
        await Task.CompletedTask;
        return Inertia.Render("home", new
        {
            message = "Welcome to Imip.Ekb Dashboard",
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application")]
    public async Task<IActionResult> ApplicationPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/create")]
    public async Task<IActionResult> ApplicationCreatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/create/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/status")]
    public async Task<IActionResult> ApplicationStatusCreatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/status/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/list")]
    public async Task<IActionResult> ApplicationListPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/list/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/draft")]
    public async Task<IActionResult> ApplicationDraftCreatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/draft/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/application/{id}/edit")]
    public async Task<IActionResult> ApplicationEditPage(Guid id)
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("application/edit/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            id,
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/approval")]
    public async Task<IActionResult> ApprovalPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("approval/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/approval/history")]
    public async Task<IActionResult> ApprovalHistoryPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("approval/history/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/manage-jetty")]
    public async Task<IActionResult> ManageJettyPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("master/manage-jetty/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/manage-vessel")]
    public async Task<IActionResult> ManageCargoPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("master/manage-vessel/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    //[HttpGet("/approval/{id}")]
    //public async Task<IActionResult> ApprovalEditPage(Guid id)
    //{
    //    await Task.CompletedTask; // Placeholder for future async operations

    //    return Inertia.Render("approval/edit", new
    //    {
    //        timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
    //        user = CurrentUser?.UserName ?? "Anonymous",
    //        id,
    //        isAuthenticated = User?.Identity?.IsAuthenticated ?? false
    //    });
    //}

    [HttpGet("/master-jetty")]
    public async Task<IActionResult> JettyPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("master/master-jetty", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/jetty/schedule")]
    public async Task<IActionResult> JettySchedulePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("jetty/schedule/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/jetty/custom-area")]
    public async Task<IActionResult> JettyCustomAreaPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("jetty/custom-area/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/jetty/non-custom-area")]
    public async Task<IActionResult> JettyNonCustomAreaPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("jetty/non-custom-area/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/master-report")]
    public async Task<IActionResult> MasterReportPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("master/report/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/jetty/docked-vessel")]
    public async Task<IActionResult> JettyDockedVesselPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("jetty/docked-vessel/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/settings")]
    public async Task<IActionResult> SettingPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("setting/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/report")]
    public async Task<IActionResult> ReportPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("report/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/approval-template")]
    public async Task<IActionResult> ApprovalTemplatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("master/approval-template/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/document-template")]
    public async Task<IActionResult> DocumentTemplatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("master/document-template/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            user = CurrentUser?.UserName ?? "Anonymous",
            isAuthenticated = User?.Identity?.IsAuthenticated ?? false
        });
    }

    [HttpGet("/jetty/vessel/edit/{id}/{docType}")]
    public async Task<IActionResult> JettyVesselEditPage(string id, string docType)
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("jetty/vessel/edit/page", new
        {
            id = id,
            type = docType
        });
    }

    [HttpGet("/jetty/vessel/create/{docType}")]
    public async Task<IActionResult> JettyVesselCreatePage(string docType)
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("jetty/vessel/create/page", new
        {
            type = docType
        });
    }

    [HttpGet("/custom-area/export")]
    public async Task<IActionResult> CustomAreaVesselExportPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("custom-area/export/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/custom-area/import")]
    public async Task<IActionResult> CustomAreaVesselImportPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("custom-area/import/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/custom-area/local")]
    public async Task<IActionResult> CustomAreaVesselLocalPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("custom-area/local/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    // non custom area

    [HttpGet("/non-custom-area/export")]
    public async Task<IActionResult> NonCustomAreaVesselExportPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("non-custom-area/export/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/non-custom-area/import")]
    public async Task<IActionResult> NonCustomAreaVesselImportPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("non-custom-area/import/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/non-custom-area/local")]
    public async Task<IActionResult> NonCustomAreaVesselLocalPage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("non-custom-area/local/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/local/create")]
    public async Task<IActionResult> NonCustomAreaVesselLocalCreatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("local/create/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/local/edit/{id}")]
    public async Task<IActionResult> NonCustomAreaVesselLocalEditPage(Guid id)
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("local/edit/page", new
        {
            id = id,
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/export/create")]
    public async Task<IActionResult> NonCustomAreaVesselExportCreatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("export/create/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/export/edit/{id}")]
    public async Task<IActionResult> NonCustomAreaVesselExportEditPage(Guid id)
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("export/edit/page", new
        {
            id = id,
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/import/create")]
    public async Task<IActionResult> NonCustomAreaVesselImportCreatePage()
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("import/create/page", new
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }

    [HttpGet("/import/edit/{id}")]
    public async Task<IActionResult> NonCustomAreaVesselImportEditPage(Guid id)
    {
        await Task.CompletedTask; // Placeholder for future async operations

        return Inertia.Render("import/edit/page", new
        {
            id = id,
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
        });
    }
}