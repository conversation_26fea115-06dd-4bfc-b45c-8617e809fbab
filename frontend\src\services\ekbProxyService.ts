/**
 * EKB Proxy Service
 *
 * This service provides a clean interface for making requests to the EKB API
 * through the backend proxy, which handles authentication and domain mapping.
 *
 * Benefits of using the proxy approach:
 * - Centralized authentication management
 * - Automatic token refresh
 * - Domain mapping to internal IPs
 * - Resilience patterns (retry, circuit breaker, timeouts)
 * - Consistent error handling
 */

import type {
  ExportVesselDto,
  ExportVesselWithItemsDto,
  ImportVesselDto,
  ImportVesselWithItemsDto,
  LocalVesselDto,
  LocalVesselWithItemsDto,
  CreateUpdateExportVesselDto,
  CreateUpdateImportVesselDto,
  CreateUpdateLocalVesselDto,
  PagedResultDtoOfExportVesselDto,
  PagedResultDtoOfImportVesselDto,
  PagedResultDtoOfLocalVesselDto,
  PagedResultDtoOfTradingVesselDto,
  PagedResultDtoOfExportVesselProjectionDto,
  PagedResultDtoOfImportVesselProjectionDto,
  PagedResultDtoOfLocalVesselProjectionDto,
  PagedResultDtoOfTradingVesselProjectionDto,
  PagedResultDtoOfAgentDto,
  PagedResultDtoOfBusinessPartnerDto,
  PagedResultDtoOfJettyDto,
  PagedResultDtoOfMasterTenantDto,
  PagedResultDtoOfCargoDto,
  PagedResultDtoOfDestinationPortDto,
  PagedResultDtoOfPortOfLoadingDto,
  ApplicationConfigurationDto,
  QueryParametersDto,
  CargoDto,
  CargoCreateUpdateDto,
  RemoteServiceErrorResponse,
} from '@/clientEkb/types.gen';

interface EkbProxyResponse<T = unknown> {
  data?: T;
  error?: string;
  status: number;
}

class EkbProxyService {
  private baseUrl = '/api/ekb-proxy';

  /**
   * Generic method for making requests through the EKB proxy
   */
  private async makeRequest<T = unknown>(
    path: string,
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' = 'GET',
    data?: unknown,
    headers?: Record<string, string>
  ): Promise<EkbProxyResponse<T>> {
    try {
      const url = `${this.baseUrl}/${path.replace(/^\//, '')}`;

      const requestOptions: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          ...headers,
        },
        credentials: 'include',
      };

      if (data && method !== 'GET' && method !== 'DELETE') {
        requestOptions.body = JSON.stringify(data);
      }

      const response = await fetch(url, requestOptions);
      const responseData = await response.text();

      let parsedData;
      try {
        parsedData = JSON.parse(responseData);
      } catch {
        parsedData = responseData;
      }

      if (!response.ok) {
        // For 4xx and 5xx status codes, throw an error to trigger onError callback
        const errorData = parsedData?.error || parsedData;
        const errorMessage = errorData?.message || errorData?.details || `HTTP ${response.status}`;
        const error = new Error(errorMessage) as any;

        // Attach the full response data to the error for detailed error handling
        error.response = {
          data: parsedData,
          status: response.status,
        };
        error.validationErrors = errorData?.validationErrors;
        error.details = errorData?.details;

        console.log("error", error);
        throw error;
      }

      return {
        data: parsedData,
        status: response.status,
      };
    } catch (error) {
      console.error('EKB Proxy request failed:', error);
      return {
        error: error instanceof Error ? error.message : 'Network error',
        status: 0,
      };
    }
  }

  /**
   * GET request through EKB proxy
   */
  async get<T = unknown>(path: string, headers?: Record<string, string>): Promise<EkbProxyResponse<T>> {
    return this.makeRequest<T>(path, 'GET', undefined, headers);
  }

  /**
   * POST request through EKB proxy
   */
  async post<T = unknown>(path: string, data?: unknown, headers?: Record<string, string>): Promise<EkbProxyResponse<T>> {
    return this.makeRequest<T>(path, 'POST', data, headers);
  }

  /**
   * PUT request through EKB proxy
   */
  async put<T = unknown>(path: string, data?: unknown, headers?: Record<string, string>): Promise<EkbProxyResponse<T>> {
    return this.makeRequest<T>(path, 'PUT', data, headers);
  }

  /**
   * PATCH request through EKB proxy
   */
  async patch<T = unknown>(path: string, data?: unknown, headers?: Record<string, string>): Promise<EkbProxyResponse<T>> {
    return this.makeRequest<T>(path, 'PATCH', data, headers);
  }

  /**
   * DELETE request through EKB proxy
   */
  async delete<T = unknown>(path: string, headers?: Record<string, string>): Promise<EkbProxyResponse<T>> {
    return this.makeRequest<T>(path, 'DELETE', undefined, headers);
  }

  /**
   * File upload through EKB proxy
   */
  async uploadFile<T = unknown>(
    path: string,
    files: File[],
    additionalData?: Record<string, string>,
    headers?: Record<string, string>
  ): Promise<EkbProxyResponse<T>> {
    try {
      const url = `${this.baseUrl}/upload/${path.replace(/^\//, '')}`;

      const formData = new FormData();

      // Add files
      files.forEach((file, index) => {
        formData.append(`file${index}`, file, file.name);
      });

      // Add additional form data
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, value);
        });
      }

      const requestOptions: RequestInit = {
        method: 'POST',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          ...headers,
          // Don't set Content-Type for FormData, let browser set it with boundary
        },
        credentials: 'include',
        body: formData,
      };

      const response = await fetch(url, requestOptions);
      const responseData = await response.text();

      let parsedData;
      try {
        parsedData = JSON.parse(responseData);
      } catch {
        parsedData = responseData;
      }

      if (!response.ok) {
        // For 4xx and 5xx status codes, throw an error to trigger onError callback
        const errorData = parsedData?.error || parsedData;
        const errorMessage = errorData?.message || errorData?.details || `HTTP ${response.status}`;
        const error = new Error(errorMessage) as any;

        // Attach the full response data to the error for detailed error handling
        error.response = {
          data: parsedData,
          status: response.status,
        };
        error.validationErrors = errorData?.validationErrors;
        error.details = errorData?.details;

        throw error;
      }

      return {
        data: parsedData,
        status: response.status,
      };
    } catch (error) {
      console.error('EKB Proxy file upload failed:', error);
      return {
        error: error instanceof Error ? error.message : 'Network error',
        status: 0,
      };
    }
  }

  /**
   * Typed methods for common EKB operations
   */

  // Export Vessel operations
  async getExportVessels(params?: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfExportVesselDto>> {
    const queryString = params ? `?${new URLSearchParams(params as Record<string, string>).toString()}` : '';
    return this.get<PagedResultDtoOfExportVesselDto>(`/api/ekb/export-vessel${queryString}`);
  }

  async getExportVessel(id: string): Promise<EkbProxyResponse<ExportVesselDto>> {
    return this.get<ExportVesselDto>(`/api/ekb/export-vessel/${id}`);
  }

  async createExportVessel(vesselData: CreateUpdateExportVesselDto): Promise<EkbProxyResponse<ExportVesselDto>> {
    return this.post<ExportVesselDto>('/api/ekb/export-vessel', vesselData);
  }

  async updateExportVessel(id: string, vesselData: CreateUpdateExportVesselDto): Promise<EkbProxyResponse<ExportVesselDto>> {
    return this.put<ExportVesselDto>(`/api/ekb/export-vessel/${id}`, vesselData);
  }

  async deleteExportVessel(id: string): Promise<EkbProxyResponse<void>> {
    return this.delete<void>(`/api/ekb/export-vessel/${id}`);
  }

  async generateNextExportVesselDocNum(postDate?: string): Promise<EkbProxyResponse<string>> {
    const queryString = postDate ? `?postDate=${encodeURIComponent(postDate)}` : '';
    return this.post<string>(`/api/ekb/export-vessel/generate-next-doc-num${queryString}`);
  }

  async getExportVesselWithItems(id: string): Promise<EkbProxyResponse<ExportVesselWithItemsDto>> {
    return this.get<ExportVesselWithItemsDto>(`/api/ekb/export-vessel/${id}/with-items`);
  }

  // Import Vessel operations
  async getImportVessels(params?: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfImportVesselDto>> {
    const queryString = params ? `?${new URLSearchParams(params as Record<string, string>).toString()}` : '';
    return this.get<PagedResultDtoOfImportVesselDto>(`/api/ekb/import-vessel${queryString}`);
  }

  async getImportVessel(id: string): Promise<EkbProxyResponse<ImportVesselDto>> {
    return this.get<ImportVesselDto>(`/api/ekb/import-vessel/${id}`);
  }

  async createImportVessel(vesselData: CreateUpdateImportVesselDto): Promise<EkbProxyResponse<ImportVesselDto>> {
    return this.post<ImportVesselDto>('/api/ekb/import-vessel', vesselData);
  }

  async updateImportVessel(id: string, vesselData: CreateUpdateImportVesselDto): Promise<EkbProxyResponse<ImportVesselDto>> {
    return this.put<ImportVesselDto>(`/api/ekb/import-vessel/${id}`, vesselData);
  }

  async generateNextImportVesselDocNum(): Promise<EkbProxyResponse<number>> {
    return this.post<number>('/api/ekb/import-vessel/generate-next-doc-num');
  }

  async getImportVesselWithItems(id: string): Promise<EkbProxyResponse<ImportVesselWithItemsDto>> {
    return this.get<ImportVesselWithItemsDto>(`/api/ekb/import-vessel/${id}/with-items`);
  }

  // Local Vessel operations
  async getLocalVessels(params?: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfLocalVesselDto>> {
    const queryString = params ? `?${new URLSearchParams(params as Record<string, string>).toString()}` : '';
    return this.get<PagedResultDtoOfLocalVesselDto>(`/api/ekb/local-vessel${queryString}`);
  }

  async getLocalVessel(id: string): Promise<EkbProxyResponse<LocalVesselDto>> {
    return this.get<LocalVesselDto>(`/api/ekb/local-vessel/${id}`);
  }

  async createLocalVessel(vesselData: CreateUpdateLocalVesselDto): Promise<EkbProxyResponse<LocalVesselDto>> {
    return this.post<LocalVesselDto>('/api/ekb/local-vessel', vesselData);
  }

  async updateLocalVessel(id: string, vesselData: CreateUpdateLocalVesselDto): Promise<EkbProxyResponse<LocalVesselDto>> {
    return this.put<LocalVesselDto>(`/api/ekb/local-vessel/${id}`, vesselData);
  }

  async getLocalVesselWithItems(id: string): Promise<EkbProxyResponse<LocalVesselWithItemsDto>> {
    return this.get<LocalVesselWithItemsDto>(`/api/ekb/local-vessel/${id}/with-items`);
  }

  async generateNextLocalVesselDocNum(postDate?: string): Promise<EkbProxyResponse<string>> {
    const queryString = postDate ? `?postDate=${encodeURIComponent(postDate)}` : '';
    return this.post<string>(`/api/ekb/local-vessel/generate-next-doc-num${queryString}`);
  }

  // Trading Vessel operations
  async getTradingVessels(params?: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfTradingVesselDto>> {
    const queryString = params ? `?${new URLSearchParams(params as Record<string, string>).toString()}` : '';
    return this.get<PagedResultDtoOfTradingVesselDto>(`/api/ekb/trading-vessel${queryString}`);
  }

  // Master data operations
  async getAgents(params: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfAgentDto>> {
    const queryString = new URLSearchParams(params as Record<string, string>).toString();
    return this.get<PagedResultDtoOfAgentDto>(`/api/master/agent/filter-list?${queryString}`);
  }

  async getBusinessPartners(params: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfBusinessPartnerDto>> {
    const queryString = new URLSearchParams(params as Record<string, string>).toString();
    return this.get<PagedResultDtoOfBusinessPartnerDto>(`/api/ekb/business-partner/filter-list?${queryString}`);
  }

  async getJetties(params: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfJettyDto>> {
    const queryString = new URLSearchParams(params as Record<string, string>).toString();
    return this.get<PagedResultDtoOfJettyDto>(`/api/ekb/jetty/filter-list?${queryString}`);
  }

  async getTenants(params: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfMasterTenantDto>> {
    const queryString = new URLSearchParams(params as Record<string, string>).toString();
    return this.get<PagedResultDtoOfMasterTenantDto>(`/api/ekb/tenant/filter-list?${queryString}`);
  }

  // Application configuration
  async getApplicationConfiguration(): Promise<EkbProxyResponse<ApplicationConfigurationDto>> {
    return this.get<ApplicationConfigurationDto>('/api/abp/application-configuration');
  }

  // File upload for vessel documents
  async uploadVesselDocuments(vesselId: string, files: File[], metadata?: Record<string, string>): Promise<EkbProxyResponse<unknown>> {
    return this.uploadFile(`/api/ekb/vessel/${vesselId}/documents`, files, metadata);
  }

  // Filter methods for vessel types using direct EKB API paths
  async filterImportVessels(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfImportVesselProjectionDto>> {
    return this.post<PagedResultDtoOfImportVesselProjectionDto>('/api/ekb/import-vessel/filter-list', filterData);
  }

  async filterExportVessels(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfExportVesselProjectionDto>> {
    return this.post<PagedResultDtoOfExportVesselProjectionDto>('/api/ekb/export-vessel/filter-list', filterData);
  }

  async filterLocalVessels(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfLocalVesselProjectionDto>> {
    return this.post<PagedResultDtoOfLocalVesselProjectionDto>('/api/ekb/local-vessel/filter-list', filterData);
  }

  async filterTradingVessels(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfTradingVesselProjectionDto>> {
    return this.post<PagedResultDtoOfTradingVesselProjectionDto>('/api/ekb/trading-vessel/filter-list', filterData);
  }

  // Filter methods for master data using direct EKB API paths
  async filterBusinessPartners(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfBusinessPartnerDto>> {
    return this.post<PagedResultDtoOfBusinessPartnerDto>('/api/ekb/business-partner/filter-list', filterData);
  }

  async filterTenants(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfMasterTenantDto>> {
    return this.post<PagedResultDtoOfMasterTenantDto>('/api/ekb/tenant/filter-list', filterData);
  }

  async filterJetties(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfJettyDto>> {
    return this.post<PagedResultDtoOfJettyDto>('/api/ekb/jetty/filter-list', filterData);
  }

  async filterAgents(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfAgentDto>> {
    return this.post<PagedResultDtoOfAgentDto>('/api/master/agent/filter-list', filterData);
  }

  // Additional filter methods for async selects using direct EKB API paths
  async filterCargo(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfCargoDto>> {
    return this.post<PagedResultDtoOfCargoDto>('/api/ekb/cargo/filter-list', filterData);
  }

  async filterDestinationPorts(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfDestinationPortDto>> {
    return this.post<PagedResultDtoOfDestinationPortDto>('/api/ekb/destination-port/filter-list', filterData);
  }

  async filterPortOfLoading(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfPortOfLoadingDto>> {
    return this.post<PagedResultDtoOfPortOfLoadingDto>('/api/ekb/port-of-loading/filter-list', filterData);
  }

  // Cargo operations
  async getCargoList(params?: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfCargoDto>> {
    const queryString = params ? `?${new URLSearchParams(params as Record<string, string>).toString()}` : '';
    return this.get<PagedResultDtoOfCargoDto>(`/api/ekb/cargo${queryString}`);
  }

  async getCargo(id: string): Promise<EkbProxyResponse<CargoDto>> {
    return this.get<CargoDto>(`/api/ekb/cargo/${id}`);
  }

  async createCargo(data: CargoCreateUpdateDto): Promise<EkbProxyResponse<CargoDto>> {
    return this.post<CargoDto>('/api/ekb/cargo', data);
  }

  async updateCargo(id: string, data: CargoCreateUpdateDto): Promise<EkbProxyResponse<CargoDto>> {
    return this.put<CargoDto>(`/api/ekb/cargo/${id}`, data);
  }

  async deleteCargo(id: string): Promise<EkbProxyResponse<void>> {
    return this.delete<void>(`/api/ekb/cargo/${id}`);
  }

  async filterCargoList(filterData: QueryParametersDto): Promise<EkbProxyResponse<PagedResultDtoOfCargoDto>> {
    return this.post<PagedResultDtoOfCargoDto>('/api/ekb/cargo/filter-list', filterData);
  }

  // File operations
  async getFileStream(id: string): Promise<EkbProxyResponse<Blob>> {
    try {
      const url = `${this.baseUrl}/api/ekb/files/stream/${id}`;
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      });
      if (!response.ok) {
        const errorText = await response.text();
        return {
          error: errorText || `HTTP ${response.status}`,
          status: response.status
        };
      }
      const blob = await response.blob();
      return {
        data: blob,
        status: response.status
      };
    } catch (error) {
      console.error('EKB Proxy file stream failed:', error);
      return {
        error: error instanceof Error ? error.message : 'Network error',
        status: 0
      };
    }
  }

  async deleteDocAttachment(id: string): Promise<EkbProxyResponse<void>> {
    return this.delete<void>(`/api/ekb/doc-attachments/${id}`);
  }
}

// Export singleton instance
export const ekbProxyService = new EkbProxyService();
export default ekbProxyService;

// Export types for use in components
export type { EkbProxyResponse };