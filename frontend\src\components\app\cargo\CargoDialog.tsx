import { ekbProxyService } from "@/services/ekbProxyService";
import type { CargoDto, CargoCreateUpdateDto, RemoteServiceErrorResponse } from "@/clientEkb/types.gen";
import { Button } from "@/components/ui/button";
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Divider } from "@/components/ui/divider";
import { FormField } from "@/components/ui/FormField";
import { Input } from "@/components/ui/input";
import { useToast } from '@/lib/useToast';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React from "react";
import { useForm } from "react-hook-form";

export type CargoDialogProps = {
  open: boolean;
  onClose: () => void;
  initialData?: Partial<CargoDto>;
  queryKey: unknown[];
};

const CargoDialog: React.FC<CargoDialogProps> = ({ open, onClose, initialData, queryKey }) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const isEdit = Boolean(initialData && initialData.id);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
  } = useForm<Partial<CargoCreateUpdateDto>>({
    defaultValues: {
      name: initialData?.name ?? "",
      alias: initialData?.alias ?? "",
      docEntry: initialData?.docEntry ?? 0,
      status: initialData?.status ?? "",
      flag: initialData?.flag ?? "",
      type: initialData?.type ?? "",
      grossWeight: initialData?.grossWeight ?? 0,
      loaQty: initialData?.loaQty ?? 0,
      createdBy: initialData?.createdBy ?? "",
    },
  });

  React.useEffect(() => {
    reset({
      name: initialData?.name ?? "",
      alias: initialData?.alias ?? "",
      docEntry: initialData?.docEntry ?? 0,
      status: initialData?.status ?? "",
      flag: initialData?.flag ?? "",
      type: initialData?.type ?? "",
      grossWeight: initialData?.grossWeight ?? 0,
      loaQty: initialData?.loaQty ?? 0,
      createdBy: initialData?.createdBy ?? "",
    });
  }, [initialData, open, reset]);

  const createMutation = useMutation({
    mutationFn: async (values: Partial<CargoCreateUpdateDto>) => {
      const payload: CargoCreateUpdateDto = {
        name: values.name ?? "",
        alias: values.alias ?? "",
        docEntry: values.docEntry ?? 0,
        status: values.status ?? "",
        flag: values.flag ?? "",
        type: values.type ?? undefined,
        grossWeight: values.grossWeight ?? 0,
        loaQty: values.loaQty ?? undefined,
        createdBy: values.createdBy ?? "",
      };
      return ekbProxyService.createCargo(payload);
    },
    onSuccess: (data) => {
      console.log("data", data)
      queryClient.invalidateQueries({ queryKey });
      onClose();
    },
    onError: (err: Error & RemoteServiceErrorResponse) => {
      console.log("error", err);

      let title = 'Error';
      let description: string | undefined = undefined;

      // Get the error message
      title = err.message || err.error?.message || 'Error';

      // Check for validation errors first
      if (err.error?.validationErrors && Array.isArray(err.error.validationErrors)) {
        description = err.error.validationErrors
          .map((error) => error.message)
          .filter(Boolean)
          .join(', ');
      } else if (err.error?.details) {
        description = err.error.details;
      }

      toast({
        title,
        description,
        variant: 'destructive',
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (values: Partial<CargoCreateUpdateDto>) => {
      if (!initialData?.id) throw new Error("No id");
      const payload: CargoCreateUpdateDto = {
        name: values.name ?? "",
        alias: values.alias ?? "",
        docEntry: values.docEntry ?? 0,
        status: values.status ?? "",
        flag: values.flag ?? "",
        type: values.type ?? undefined,
        grossWeight: values.grossWeight ?? 0,
        loaQty: values.loaQty ?? undefined,
        createdBy: values.createdBy ?? "",
      };
      return ekbProxyService.updateCargo(initialData.id, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
      onClose();
    },
    onError: (err: Error & RemoteServiceErrorResponse) => {
      console.log("error", err);

      let title = 'Error';
      let description: string | undefined = undefined;

      // Get the error message
      title = err.message || err.error?.message || 'Error';

      // Check for validation errors first
      if (err.error?.validationErrors && Array.isArray(err.error.validationErrors)) {
        description = err.error.validationErrors
          .map((error) => error.message)
          .filter(Boolean)
          .join(', ');
      } else if (err.error?.details) {
        description = err.error.details;
      }

      toast({
        title,
        description,
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (values: Partial<CargoCreateUpdateDto>) => {
    if (isEdit) {
      updateMutation.mutate(values);
    } else {
      createMutation.mutate(values);
    }
  };

  return (
    <Dialog open={open} onOpenChange={v => { if (!v) onClose(); }}>
      <DialogContent size="md">
        <DialogHeader>
          <DialogTitle>{isEdit ? "Edit Cargo" : "Create Cargo"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <FormField label="Name" labelWidth="120px">
            <Input
              {...register("name", { required: "Name is required" })}
              aria-invalid={!!errors.name}
              aria-label="Cargo Name"
              autoFocus
            />
            {errors.name && <span className="text-red-500 text-xs">{errors.name.message as string}</span>}
          </FormField>
          <FormField label="Alias" labelWidth="120px">
            <Input
              {...register("alias")}
              aria-invalid={!!errors.alias}
              aria-label="Alias"
            />
            {errors.alias && <span className="text-red-500 text-xs">{errors.alias.message as string}</span>}
          </FormField>
          <FormField label="Status" labelWidth="120px">
            <Select
              value={watch('status') || ''}
              onValueChange={value => setValue('status', value, { shouldValidate: true })}
            >
              <SelectTrigger aria-label="Status" aria-invalid={!!errors.status}>
                <SelectValue placeholder="Select Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Y">Active</SelectItem>
                <SelectItem value="N">Inactive</SelectItem>
              </SelectContent>
            </Select>
            {errors.status && <span className="text-red-500 text-xs">{errors.status.message as string}</span>}
          </FormField>
          <FormField label="Flag" labelWidth="120px">
            <Input
              {...register("flag")}
              aria-invalid={!!errors.flag}
              aria-label="Flag"
            />
            {errors.flag && <span className="text-red-500 text-xs">{errors.flag.message as string}</span>}
          </FormField>
          <FormField label="Type" labelWidth="120px">
            <Select
              value={watch("type") || ""}
              onValueChange={value => setValue("type", value, { shouldValidate: true })}
            >
              <SelectTrigger aria-label="Type" aria-invalid={!!errors.type}>
                <SelectValue placeholder="Select Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MV">MV</SelectItem>
                <SelectItem value="MT">MT</SelectItem>
                <SelectItem value="TUG/BG">TUG/BG</SelectItem>
                <SelectItem value="VIA AIR">VIA AIR</SelectItem>
                <SelectItem value="LCT">LCT</SelectItem>
              </SelectContent>
            </Select>
            {errors.type && <span className="text-red-500 text-xs">{errors.type.message as string}</span>}
          </FormField>
          <FormField label="Gross Weight" labelWidth="120px">
            <Input
              type="number"
              {...register("grossWeight", {
                valueAsNumber: true,
                validate: v => !isNaN(v as number) || "Gross Weight must be a number",
              })}
              aria-invalid={!!errors.grossWeight}
              aria-label="Gross Weight"
            />
            {errors.grossWeight && <span className="text-red-500 text-xs">{errors.grossWeight.message as string}</span>}
          </FormField>
          <FormField label="LOA Qty" labelWidth="120px">
            <Input
              type="number"
              {...register("loaQty", {
                valueAsNumber: true,
                validate: v => v === undefined || v === null || !isNaN(v as number) || "LOA Qty must be a number",
              })}
              aria-invalid={!!errors.loaQty}
              aria-label="LOA Qty"
            />
            {errors.loaQty && <span className="text-red-500 text-xs">{errors.loaQty.message as string}</span>}
          </FormField>
          <Divider className="my-2" />
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
            </DialogClose>
            <Button type="submit" disabled={isSubmitting || createMutation.isPending || updateMutation.isPending}>
              {isEdit ? "Save Changes" : "Create"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CargoDialog;