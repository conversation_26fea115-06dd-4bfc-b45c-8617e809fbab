using Imip.JettyApproval.Web.Controllers;
using Imip.JettyApproval.Web.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Security;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Services;

public class AppToAppService : IDisposable
{
    private readonly ITokenService _tokenService;
    private readonly SilentTokenRefreshService _silentTokenRefreshService;
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AppToAppService> _logger;

    // Domain to IP mapping for internal services
    private readonly Dictionary<string, string> _domainMapping;

    public AppToAppService(
        ITokenService tokenService,
        SilentTokenRefreshService silentTokenRefreshService,
        HttpClient httpClient,
        IConfiguration configuration,
        ILogger<AppToAppService> logger)
    {
        _tokenService = tokenService;
        _silentTokenRefreshService = silentTokenRefreshService;
        _configuration = configuration;
        _logger = logger;

        // Create a new HttpClient with SSL validation bypassed for internal services
        var handler = new HttpClientHandler();
        handler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator;

        _httpClient = new HttpClient(handler);
        _httpClient.Timeout = TimeSpan.FromSeconds(45);

        _logger.LogInformation("AppToAppService initialized with SSL certificate validation bypassed");

        // Initialize domain mapping for internal services
        _domainMapping = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "ekb-dev.imip.co.id", "**********" },
            { "ekb.imip.co.id", "**********" },
            { "api-identity-dev.imip.co.id", "**********" },
            { "identity.imip.co.id", "**********" }
        };
    }

    /// <summary>
    /// Calls another application using the SSO token with OIDC-compliant silent refresh
    /// Supports GET, POST, PUT, and DELETE methods.
    /// </summary>
    public async Task<T> CallOtherAppAsync<T>(string appUrl, string endpoint, HttpMethod method, object data = null)
    {
        try
        {
            var silentRefreshSuccess = await _silentTokenRefreshService.TryRefreshTokenAsync();
            if (silentRefreshSuccess)
            {
                _logger.LogDebug("Silent token refresh completed successfully");
            }

            var accessToken = await _tokenService.GetValidAccessTokenAsync();

            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogWarning("No valid access token available for app-to-app call");
                var isRefreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();
                if (isRefreshTokenExpired)
                {
                    _logger.LogWarning("Refresh token is expired, user needs to re-authenticate");
                    throw new UnauthorizedAccessException("Authentication required. Please log in again.");
                }
                else
                {
                    throw new UnauthorizedAccessException("Token refresh required. Please try again.");
                }
            }

            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", accessToken);
            _httpClient.DefaultRequestHeaders.Remove("X-App-Name");
            _httpClient.DefaultRequestHeaders.Remove("X-App-Version");
            _httpClient.DefaultRequestHeaders.Add("X-App-Name", "JettyApproval");
            _httpClient.DefaultRequestHeaders.Add("X-App-Version", "1.0");

            var fullUrl = $"{appUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";
            _logger.LogInformation("Calling other app: {Url} [{Method}]", fullUrl, method);

            HttpRequestMessage request = new(method, fullUrl);
            if (data != null && (method == HttpMethod.Post || method == HttpMethod.Put))
            {
                if (data is HttpContent httpContent)
                {
                    request.Content = httpContent;
                }
                else
                {
                    var json = JsonSerializer.Serialize(data);
                    request.Content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
                }
            }

            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("App-to-app call returned 401, attempting silent token refresh");
                var refreshSuccess = await _silentTokenRefreshService.TryRefreshTokenAsync();
                if (refreshSuccess)
                {
                    var newToken = await _tokenService.GetValidAccessTokenAsync();
                    if (!string.IsNullOrEmpty(newToken))
                    {
                        _httpClient.DefaultRequestHeaders.Authorization =
                            new AuthenticationHeaderValue("Bearer", newToken);
                        request = new HttpRequestMessage(method, fullUrl);
                        if (data != null && (method == HttpMethod.Post || method == HttpMethod.Put))
                        {
                            if (data is HttpContent httpContentRetry)
                            {
                                request.Content = httpContentRetry;
                            }
                            else
                            {
                                var jsonRetry = JsonSerializer.Serialize(data);
                                request.Content = new StringContent(jsonRetry, System.Text.Encoding.UTF8, "application/json");
                            }
                        }
                        response = await _httpClient.SendAsync(request);
                        if (response.IsSuccessStatusCode)
                        {
                            var responseContent = await response.Content.ReadAsStringAsync();
                            return JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions
                            {
                                PropertyNameCaseInsensitive = true
                            });
                        }
                    }
                }
                var isRefreshTokenExpired = await _tokenService.IsRefreshTokenExpiredAsync();
                if (isRefreshTokenExpired)
                {
                    _logger.LogWarning("Refresh token is expired, user needs to re-authenticate");
                    throw new UnauthorizedAccessException("Authentication required. Please log in again.");
                }
                else
                {
                    throw new UnauthorizedAccessException("App-to-app call failed after token refresh");
                }
            }
            else
            {
                _logger.LogError("App-to-app call failed with status: {StatusCode}", response.StatusCode);

                // Try to read the error response content to extract validation details
                var errorContent = await response.Content.ReadAsStringAsync();
                var statusCode = (int)response.StatusCode;

                try
                {
                    // Try to parse the error response as JSON to extract validation errors
                    var errorResponse = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    string message = "Request failed";
                    string details = null;
                    object validationErrors = null;

                    if (errorResponse.TryGetProperty("message", out var messageElement))
                    {
                        message = messageElement.GetString();
                    }

                    if (errorResponse.TryGetProperty("details", out var detailsElement))
                    {
                        details = detailsElement.GetString();
                    }

                    if (errorResponse.TryGetProperty("validationErrors", out var validationErrorsElement))
                    {
                        validationErrors = JsonSerializer.Deserialize<object>(validationErrorsElement.GetRawText());
                    }

                    // Pass the complete raw JSON response as ErrorDetails so the controller can return it as-is
                    throw new EkbServiceException(message, statusCode, validationErrors, errorContent);
                }
                catch (JsonException)
                {
                    // If we can't parse the response as JSON, throw a generic exception with the raw content
                    throw new EkbServiceException($"App-to-app call failed with status: {response.StatusCode}", statusCode, null, errorContent);
                }
            }
        }
        catch (UnauthorizedAccessException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in app-to-app call");
            throw;
        }
    }

    /// <summary>
    /// Enhanced proxy method that supports domain mapping and all HTTP methods
    /// </summary>
    public async Task<T> ProxyRequestAsync<T>(string targetUrl, HttpMethod method, object data = null, Dictionary<string, string> headers = null)
    {
        try
        {
            // Parse the target URL and apply domain mapping if needed
            var mappedUrl = ApplyDomainMapping(targetUrl);

            var silentRefreshSuccess = await _silentTokenRefreshService.TryRefreshTokenAsync();
            if (silentRefreshSuccess)
            {
                _logger.LogDebug("Silent token refresh completed successfully for proxy request");
            }

            var accessToken = await _tokenService.GetValidAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                _logger.LogWarning("No valid access token available for proxy request");
                throw new UnauthorizedAccessException("Authentication required. Please log in again.");
            }

            // Configure request headers
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            _httpClient.DefaultRequestHeaders.Remove("X-App-Name");
            _httpClient.DefaultRequestHeaders.Remove("X-App-Version");
            _httpClient.DefaultRequestHeaders.Add("X-App-Name", "JettyApproval");
            _httpClient.DefaultRequestHeaders.Add("X-App-Version", "1.0");

            // Add custom headers if provided
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    _httpClient.DefaultRequestHeaders.Remove(header.Key);
                    _httpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
                }
            }

            _logger.LogInformation("Proxying request: {Url} [{Method}]", mappedUrl, method);

            HttpRequestMessage request = new(method, mappedUrl);

            // Handle request body for POST, PUT, PATCH
            if (data != null && (method == HttpMethod.Post || method == HttpMethod.Put || method.Method == "PATCH"))
            {
                if (data is HttpContent httpContent)
                {
                    request.Content = httpContent;
                }
                else if (data is MultipartFormDataContent multipartContent)
                {
                    request.Content = multipartContent;
                }
                else
                {
                    var json = JsonSerializer.Serialize(data);
                    request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                }
            }

            var response = await _httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();

                // Handle different response types
                if (typeof(T) == typeof(string))
                {
                    return (T)(object)responseContent;
                }
                else if (typeof(T) == typeof(HttpResponseMessage))
                {
                    return (T)(object)response;
                }
                else
                {
                    return JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("Proxy request returned 401, attempting silent token refresh");
                var refreshSuccess = await _silentTokenRefreshService.TryRefreshTokenAsync();
                if (refreshSuccess)
                {
                    var newToken = await _tokenService.GetValidAccessTokenAsync();
                    if (!string.IsNullOrEmpty(newToken))
                    {
                        // Retry the request with new token
                        return await ProxyRequestAsync<T>(targetUrl, method, data, headers);
                    }
                }
                throw new UnauthorizedAccessException("Proxy request failed after token refresh");
            }
            else
            {
                _logger.LogError("Proxy request failed with status: {StatusCode}", response.StatusCode);

                // Try to read the error response content to extract validation details
                var errorContent = await response.Content.ReadAsStringAsync();
                var statusCode = (int)response.StatusCode;

                try
                {
                    // Try to parse the error response as JSON to extract validation errors
                    var errorResponse = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    string message = "Request failed";
                    string details = null;
                    object validationErrors = null;

                    if (errorResponse.TryGetProperty("message", out var messageElement))
                    {
                        message = messageElement.GetString();
                    }

                    if (errorResponse.TryGetProperty("details", out var detailsElement))
                    {
                        details = detailsElement.GetString();
                    }

                    if (errorResponse.TryGetProperty("validationErrors", out var validationErrorsElement))
                    {
                        validationErrors = JsonSerializer.Deserialize<object>(validationErrorsElement.GetRawText());
                    }

                    // Pass the complete raw JSON response as ErrorDetails so the controller can return it as-is
                    throw new EkbServiceException(message, statusCode, validationErrors, errorContent);
                }
                catch (JsonException)
                {
                    // If we can't parse the response as JSON, throw a generic exception with the raw content
                    throw new EkbServiceException($"Proxy request failed with status: {response.StatusCode}", statusCode, null, errorContent);
                }
            }
        }
        catch (UnauthorizedAccessException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in proxy request to {TargetUrl}", targetUrl);
            throw;
        }
    }

    /// <summary>
    /// Proxy method specifically for file uploads with multipart/form-data
    /// </summary>
    public async Task<T> ProxyFileUploadAsync<T>(string targetUrl, MultipartFormDataContent formData, Dictionary<string, string> headers = null)
    {
        return await ProxyRequestAsync<T>(targetUrl, HttpMethod.Post, formData, headers);
    }

    /// <summary>
    /// Apply domain mapping to convert external domains to internal IPs
    /// </summary>
    private string ApplyDomainMapping(string url)
    {
        try
        {
            var uri = new Uri(url);
            var hostname = uri.Host;

            if (_domainMapping.TryGetValue(hostname, out var internalIp))
            {
                _logger.LogDebug("Mapping domain {Domain} to internal IP {InternalIp}", hostname, internalIp);

                var scheme = uri.Scheme;
                var port = uri.Port > 0 && uri.Port != 80 && uri.Port != 443 ? $":{uri.Port}" : "";
                var pathAndQuery = uri.PathAndQuery;

                var mappedUrl = $"{scheme}://{internalIp}{port}{pathAndQuery}";
                _logger.LogDebug("Mapped URL: {MappedUrl}", mappedUrl);
                return mappedUrl;
            }

            return url;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error applying domain mapping to URL {Url}, using original URL", url);
            return url;
        }
    }

    /// <summary>
    /// Example: Call another app's API (GET)
    /// </summary>
    public async Task<object> GetDataFromOtherAppAsync()
    {
        var otherAppUrl = _configuration["ExternalApps:OtherApp:BaseUrl"];
        return await CallOtherAppAsync<object>(otherAppUrl, "/api/data", HttpMethod.Get);
    }

    /// <summary>
    /// Example: Send data to another app (POST)
    /// </summary>
    public async Task<object> SendDataToOtherAppAsync(object data)
    {
        var otherAppUrl = _configuration["ExternalApps:OtherApp:BaseUrl"];
        return await CallOtherAppAsync<object>(otherAppUrl, "/api/data", HttpMethod.Post, data);
    }

    /// <summary>
    /// Example: Update data in another app (PUT)
    /// </summary>
    public async Task<object> UpdateDataInOtherAppAsync(object data)
    {
        var otherAppUrl = _configuration["ExternalApps:OtherApp:BaseUrl"];
        return await CallOtherAppAsync<object>(otherAppUrl, "/api/data", HttpMethod.Put, data);
    }

    /// <summary>
    /// Example: Delete data in another app (DELETE)
    /// </summary>
    public async Task<object> DeleteDataInOtherAppAsync(string id)
    {
        var otherAppUrl = _configuration["ExternalApps:OtherApp:BaseUrl"];
        var endpoint = $"/api/data/{id}";
        return await CallOtherAppAsync<object>(otherAppUrl, endpoint, HttpMethod.Delete);
    }

    /// <summary>
    /// Streams a file from another application and returns a FileStreamResult.
    /// </summary>
    public async Task<FileStreamResult> StreamFileFromOtherAppAsync(string appUrl, string endpoint, HttpMethod method)
    {
        var fullUrl = $"{appUrl.TrimEnd('/')}/{endpoint.TrimStart('/')}";
        var silentRefreshSuccess = await _silentTokenRefreshService.TryRefreshTokenAsync();
        if (silentRefreshSuccess)
        {
            _logger.LogDebug("Silent token refresh completed successfully");
        }
        var accessToken = await _tokenService.GetValidAccessTokenAsync();
        if (string.IsNullOrEmpty(accessToken))
        {
            _logger.LogWarning("No valid access token available for file stream");
            throw new UnauthorizedAccessException("Authentication required. Please log in again.");
        }
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        _httpClient.DefaultRequestHeaders.Remove("X-App-Name");
        _httpClient.DefaultRequestHeaders.Remove("X-App-Version");
        _httpClient.DefaultRequestHeaders.Add("X-App-Name", "JettyApproval");
        _httpClient.DefaultRequestHeaders.Add("X-App-Version", "1.0");
        var request = new HttpRequestMessage(method, fullUrl);
        var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
        if (!response.IsSuccessStatusCode)
        {
            _logger.LogError("File stream proxy failed with status: {StatusCode}", response.StatusCode);
            throw new HttpRequestException($"File stream proxy failed with status: {response.StatusCode}");
        }
        var stream = await response.Content.ReadAsStreamAsync();
        var contentType = response.Content.Headers.ContentType?.ToString() ?? "application/octet-stream";
        var fileName = response.Content.Headers.ContentDisposition?.FileNameStar ?? response.Content.Headers.ContentDisposition?.FileName;
        if (!string.IsNullOrEmpty(fileName))
        {
            return new FileStreamResult(stream, contentType)
            {
                FileDownloadName = fileName
            };
        }
        return new FileStreamResult(stream, contentType);
    }

    /// <summary>
    /// Dispose of the HttpClient
    /// </summary>
    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}