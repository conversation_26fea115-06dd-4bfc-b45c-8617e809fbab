import { ekbProxyService } from '@/services/ekbProxyService';
import type { BusinessPartnerDto, CreateUpdateImportVesselDto, CreateUpdateVesselItemDto, MasterTenantDto } from '@/clientEkb/types.gen';
import { Button } from '@/components/ui/button';
import { FormField, FormSection } from '@/components/ui/FormField';
import { Input } from '@/components/ui/input';
import { HotTable, type HotTableRef } from '@handsontable/react-wrapper';
import { zodResolver } from '@hookform/resolvers/zod';
import type { QueryClient } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { registerAllModules } from 'handsontable/registry';
import 'handsontable/styles/handsontable.min.css';
import 'handsontable/styles/ht-theme-horizon.css';
import 'handsontable/styles/ht-theme-main.min.css';
import type { ColumnSettings } from 'node_modules/handsontable/settings';
import { useEffect, useRef, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { DestinationPortSelect, JettySelect, PortOfLoadingSelect, VesselSelect } from '../export/async-selects';
import { getImportVesselColumns, renderAttachmentButton } from './import-vessel-columns';
import { type ImportVesselHeaderForm, importVesselHeaderSchema } from './import-vessel-header-schema';
import { type ImportVesselItemForm, importVesselItemSchema } from './import-vessel-item-schema';
registerAllModules();

export type ImportVesselFormProps = {
  mode: 'create' | 'edit';
  initialHeader: Partial<CreateUpdateImportVesselDto>;
  initialItems: CreateUpdateVesselItemDto[];
  onSubmit: (header: ImportVesselHeaderForm, items: ImportVesselItemForm[]) => Promise<void>;
  columns?: ColumnSettings[];
  headerSchema?: typeof importVesselHeaderSchema;
  itemSchema?: typeof importVesselItemSchema;
  isSubmitting?: boolean;
  title?: string;
  tenants: MasterTenantDto[];
  businessPartners: BusinessPartnerDto[];
  queryClient: QueryClient;
  showAddLineButton?: boolean;
};

function toImportVesselHeaderForm(dto: Partial<CreateUpdateImportVesselDto>): ImportVesselHeaderForm {
  return {
    docNum: dto.docNum?.toString() ?? '',
    vesselId: dto.vesselId ?? '',
    voyage: dto.voyage ?? '',
    postingDate: dto.postingDate ?? '',
    vesselArrival: dto.vesselArrival ?? '',
    vesselDeparture: dto.vesselDeparture ?? '',
    portOriginId: dto.portOriginId ?? '',
    destinationPortId: dto.destinationPortId ?? '',
    jettyId: dto.jettyId ?? '',
    deleted: dto.deleted ?? '',
    docType: dto.docType ?? '',
    isChange: dto.isChange ?? '',
    isLocked: dto.isLocked ?? '',
    createdBy: dto.createdBy ?? '',
    docStatus: dto.docStatus ?? '',
    statusBms: dto.statusBms ?? '',
    transType: dto.transType ?? '',
    concurrencyStamp: dto.concurrencyStamp ?? '',
    asideDate: dto.asideDate ?? '',
    castOfDate: dto.castOfDate ?? '',
  };
}

// Extend ImportVesselItemForm to always include concurrencyStamp and id for form logic
type ImportVesselItemFormWithConcurrency = ImportVesselItemForm & { concurrencyStamp?: string; id?: string };

// Wrapper component that handles data fetching
export const ImportVesselFormWithData: React.FC<Omit<ImportVesselFormProps, 'columns' | 'tenants' | 'businessPartners'> & { queryClient: QueryClient }> = (props) => {
  const { data: tenants = [], isLoading: loadingTenants } = useQuery({
    queryKey: ['tenants'],
    queryFn: () =>
      ekbProxyService.filterTenants({ page: 1, maxResultCount: 1000 })
        .then(res => res.data?.items ?? []),
  });

  const { data: businessPartners = [], isLoading: loadingBusinessPartners } = useQuery({
    queryKey: ['businessPartners'],
    queryFn: () =>
      ekbProxyService.filterBusinessPartners({ page: 1, maxResultCount: 10000 })
        .then(res => res.data?.items ?? []),
  });

  if (loadingTenants || loadingBusinessPartners) return <div>Loading data...</div>;

  return (
    <ImportVesselForm
      {...props}
      columns={getImportVesselColumns(tenants, businessPartners, props.queryClient)}
      tenants={tenants}
      businessPartners={businessPartners}
      queryClient={props.queryClient}
    />
  );
};

// Main form component that receives data as props
const ImportVesselForm: React.FC<ImportVesselFormProps> = ({
  mode,
  initialHeader,
  initialItems,
  onSubmit,
  columns,
  headerSchema = importVesselHeaderSchema,
  itemSchema = importVesselItemSchema,
  isSubmitting = false,
  title = 'Create Import Vessel',
  tenants,
  businessPartners,
  queryClient,
  showAddLineButton = true,
}) => {
  const resolvedColumns = columns ?? getImportVesselColumns([], [], queryClient);
  const [items, setItems] = useState<ImportVesselItemForm[]>(initialItems as ImportVesselItemForm[]);
  const [itemErrors, setItemErrors] = useState<string[]>([]);
  const hotTableRef = useRef<HotTableRef | null>(null);
  const prevKey = useRef<string | undefined>(null);

  const methods = useForm<ImportVesselHeaderForm>({
    resolver: zodResolver(headerSchema),
    defaultValues: toImportVesselHeaderForm(initialHeader),
    mode: 'onBlur',
  });
  const { register, handleSubmit, formState: { errors }, reset } = methods;

  useEffect(() => {
    const key = String(initialHeader.docNum ?? '');
    console.log("check key", key, prevKey.current);
    if (mode === 'edit' && key && prevKey.current !== key) {
      reset(toImportVesselHeaderForm(initialHeader));
      setItems(initialItems.map(i => ({ ...i, concurrencyStamp: i.concurrencyStamp ?? undefined, id: i.id ?? undefined })));
      prevKey.current = key;
    }
  }, [initialHeader, initialItems, mode, reset]);

  const validateItems = (data: ImportVesselItemForm[]): boolean => {
    const errors: string[] = [];
    data.forEach((item, idx) => {
      const result = itemSchema.safeParse(item);
      if (!result.success) {
        errors[idx] = Object.values(result.error.flatten().fieldErrors).flat().join(', ');
      } else {
        errors[idx] = '';
      }
    });
    setItemErrors(errors);
    return errors.every(e => !e);
  };

  const onFormSubmit = async (header: ImportVesselHeaderForm) => {
    const currentItems = (hotTableRef.current?.hotInstance?.getSourceData?.() ?? []) as ImportVesselItemForm[];

    // Transform tenant names to tenant IDs and business partner names to business partner IDs
    const transformedItems = currentItems.map(item => {
      const transformedItem: ImportVesselItemFormWithConcurrency = { ...item, concurrencyStamp: item.concurrencyStamp ?? undefined, id: item.id ?? undefined };

      if (item.tenant) {
        // Find tenant by name and set tenantId
        const tenant = tenants.find(t => t.name === item.tenant);
        if (tenant) {
          transformedItem.tenantId = tenant.id || '';
        }
      }

      if (item.businessPartner) {
        // Find business partner by name and set businessPartnerId
        const businessPartner = businessPartners.find(bp => bp.name === item.businessPartner);
        if (businessPartner) {
          transformedItem.businessPartnerId = businessPartner.id || '';
        }
      }

      // When preparing the PUT payload, always use the latest concurrencyStamp from initialItems (API data)
      const latest = (initialItems as ImportVesselItemFormWithConcurrency[]).find(i => i.id === item.id);
      if (latest && latest.concurrencyStamp) {
        transformedItem.concurrencyStamp = latest.concurrencyStamp;
      }

      return transformedItem;
    });

    if (!validateItems(transformedItems)) return;
    await onSubmit(header, transformedItems);
  };

  const handleAddLine = () => {
    setItems(prev => [...prev, {} as ImportVesselItemForm]);
  };

  function handleApproval() {
    // TODO: Implement approval logic
  }

  // Create column configuration with renderer
  const columnConfig = resolvedColumns.map(col => {
    if (col.data === 'id') {
      return {
        ...col,
        renderer: renderAttachmentButton(queryClient)
      };
    }
    return col;
  });

  // Get masterJetty from the selected jetty (if available)
  type Jetty = { id: string; isCustomArea?: boolean };
  const jettyList: Jetty[] = []; // TODO: replace with actual jetty list
  const selectedJetty = jettyList.find(j => j.id === methods.watch('jettyId')) || null;

  return (
    <div className="w-full mx-auto">
      <div className='bg-card text-card-foreground rounded-lg border shadow-sm px-4 py-4'>
        <div className="mb-6">
          <h2 className="text-lg font-bold text-gray-800 dark:text-white">{title}</h2>
          <div className="h-1 w-16 bg-primary rounded mt-2 mb-4" />
        </div>
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormSection showDivider={false}>
                <FormField label="DocNum" labelWidth='100px'>
                  <Input {...register('docNum')} disabled={mode === 'edit'} />
                  {errors.docNum && (
                    <span className="text-red-500 text-xs">{errors.docNum.message as string}</span>
                  )}
                </FormField>

                <FormField label="Vessel Name" labelWidth='100px'>
                  <Controller
                    name="vesselId"
                    control={methods.control}
                    render={({ field }) => (
                      <VesselSelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select vessel..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.vesselId && (
                    <span className="text-red-500 text-xs">{errors.vesselId.message as string}</span>
                  )}
                </FormField>

                <FormField label="Voyage" labelWidth='100px'>
                  <Input {...register('voyage')} />
                  {errors.voyage && (
                    <span className="text-red-500 text-xs">{errors.voyage.message as string}</span>
                  )}
                </FormField>

                <FormField label="Jetty" labelWidth='100px'>
                  <Controller
                    name="jettyId"
                    control={methods.control}
                    render={({ field }) => (
                      <JettySelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select jetty..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.jettyId && (
                    <span className="text-red-500 text-xs">{errors.jettyId.message as string}</span>
                  )}
                </FormField>
              </FormSection>

              <FormSection showDivider={false}>
                <FormField label="A/Side Date" labelWidth='100px'>
                  <Input type="datetime-local" {...register('asideDate')} />
                  {errors.asideDate && (
                    <span className="text-red-500 text-xs">{errors.asideDate.message as string}</span>
                  )}
                </FormField>

                <FormField label="Cast Of Date" labelWidth='100px'>
                  <Input type="datetime-local" {...register('castOfDate')} />
                  {errors.castOfDate && (
                    <span className="text-red-500 text-xs">{errors.castOfDate.message as string}</span>
                  )}
                </FormField>

                <FormField label="Arrival Date" labelWidth='100px'>
                  <Input type="datetime-local" {...register('vesselArrival')} />
                  {errors.vesselArrival && (
                    <span className="text-red-500 text-xs">{errors.vesselArrival.message as string}</span>
                  )}
                </FormField>

                <FormField label="Departure Date" labelWidth='100px'>
                  <Input type="datetime-local" {...register('vesselDeparture')} />
                  {errors.vesselDeparture && (
                    <span className="text-red-500 text-xs">{errors.vesselDeparture.message as string}</span>
                  )}
                </FormField>
              </FormSection>

              <FormSection showDivider={false}>
                <FormField label="Posting Date" labelWidth='100px'>
                  <Input type="date" {...register('postingDate')} />
                  {errors.postingDate && (
                    <span className="text-red-500 text-xs">{errors.postingDate.message as string}</span>
                  )}
                </FormField>

                <FormField label="Port Origin" labelWidth='100px'>
                  <Controller
                    name="portOriginId"
                    control={methods.control}
                    render={({ field }) => (
                      <PortOfLoadingSelect
                        value={field.value ?? ''}
                        onValueChange={field.onChange}
                        placeholder="Select port origin..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.portOriginId && (
                    <span className="text-red-500 text-xs">{errors.portOriginId.message as string}</span>
                  )}
                </FormField>

                <FormField label="Destination Port" labelWidth='100px'>
                  <Controller
                    name="destinationPortId"
                    control={methods.control}
                    render={({ field }) => (
                      <DestinationPortSelect
                        value={field.value ?? ''}
                        onValueChange={field.onChange}
                        placeholder="Select destination port..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.destinationPortId && (
                    <span className="text-red-500 text-xs">{errors.destinationPortId.message as string}</span>
                  )}
                </FormField>
              </FormSection>
            </div>
            <div className="mt-6">
              <label className="block font-medium mb-2">Items</label>
              <HotTable
                ref={hotTableRef}
                themeName="ht-theme-main"
                data={items}
                columns={columnConfig}
                colHeaders={columnConfig.map(col => col.title).filter((t): t is string => typeof t === 'string')}
                rowHeaders={true}
                height="50vh"
                licenseKey="non-commercial-and-evaluation"
                stretchH="all"
                contextMenu={true}
                manualColumnResize={true}
                manualRowResize={true}
                autoColumnSize={false}
                autoRowSize={false}
                startRows={1}
                dropdownMenu={true}
                filters={true}
                colWidths={80}
                hiddenColumns={{
                  copyPasteEnabled: true,
                  indicators: true,
                  columns: [0, 1, 2]
                }}
                width="100%"
                persistentState={true}
              />
              {itemErrors.some(e => e) && (
                <div className="mt-2 text-red-500 text-xs">
                  {itemErrors.map((err, idx) => err && <div key={idx}>Row {idx + 1}: {err}</div>)}
                </div>
              )}
            </div>
            <div className="flex justify-end gap-2">
              {showAddLineButton && (
                <Button type="button" variant="outline" onClick={handleAddLine} disabled={isSubmitting}>
                  + Add Line
                </Button>
              )}
              {/* Approval button logic */}
              {typeof selectedJetty?.isCustomArea === 'boolean' && !selectedJetty.isCustomArea && (
                <Button type="button" variant="primary" onClick={handleApproval} disabled={isSubmitting}>
                  Submit Approval
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (mode === 'edit' ? 'Saving...' : 'Creating...') : (mode === 'edit' ? 'Save Changes' : 'Create')}
              </Button>
            </div>
          </form>
        </FormProvider>
      </div>
    </div>
  );
};

export default ImportVesselForm; 